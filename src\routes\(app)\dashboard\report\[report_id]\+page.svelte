<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { toast } from 'svelte-sonner';
    import CustomDropdown from '$lib/components/misc/CustomDropdown.svelte';
    import MultiImageUploader from '$lib/components/misc/MultiImageUploader.svelte';
    import type { PageProps } from './$types';
    import Head from "$lib/components/layout/Head.svelte";
    import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';

    import type { Label } from '$lib/types/tables';
    import type { Site } from '$lib/types/tables';
    import type { Report } from '$lib/types/tables';
    import type { GetReportSitesRequest } from '$lib/types/requests';
    import type { GetReportResponse, SaveReportSiteResponse, GenerateReportResponse, GetLabelsResponse } from '$lib/types/responses';
    import { getLabelbyId, getLabelByName } from '$lib/utils/labels';
    import { formatVisits } from '$lib/utils/format';

    let { data }: PageProps = $props();
    let report = data.report as Report;

    const perPage = 500;

    interface ReportSiteWithLabels extends Site {
        checked: boolean;
        screenshot_urls: string[];
        label_ids: string[];
    }

    let reportSites = $state<ReportSiteWithLabels[]>([]);

    let labels = $state<Label[]>([]);
    let labelsByGroup = $state<Record<string, Label[]>>({});
    let isLoading = $state(true);
    let unsavedChanges = $state<Record<string, boolean>>({});
    let saveTimers = $state<Record<string, number>>({});

    let checkedCount = $derived.by(() => {
        return reportSites.filter((reportSite) => reportSite.checked).length;
    });

    let selectOptions = $state<Record<string, string>>({
        type: '',
        pricing: '',
        status: '',
        link_type: '',
        login: '',
        author_info: '',
        confirmation: '',
        sort_by: 'dr'
    });

    const sortOptions = [
        { label_id: 'dr', label_name: 'DR' },
        { label_id: 'visits', label_name: 'Visits' }
    ];

    async function getReportSites(): Promise<void> {
        const selectedLabelIds = ['type', 'pricing', 'link_type', 'status', 'login', 'author_info', 'confirmation']
            .filter(key => selectOptions[key])
            .map(key => selectOptions[key]);
        try {
            isLoading = true;
            const body: GetReportSitesRequest = {
                report_id: report.report_id,
                label_ids: selectedLabelIds,
                page_num: 1,
                per_page: perPage,
                sort: selectOptions.sort_by
            };
            const response = await fetch(`/api/GetReportSites/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(body)
            });
            const data = await response.json() as GetReportResponse;

            if (data.status === 'success') {
                reportSites = data.report_sites || [];
                unsavedChanges = {};
            } else {
                toast.error(data.message || 'Failed to load report');
            }
        } catch (error) {
            console.error('Unexpected error fetching report:', error);
            toast.error('Unexpected error: Failed to load report');
        } finally {
            isLoading = false;
        }
    }

    async function saveReportSite(reportSite: ReportSiteWithLabels): Promise<void> {
        if (saveTimers[reportSite.site_id]) {
            clearTimeout(saveTimers[reportSite.site_id]);
            delete saveTimers[reportSite.site_id];
        }
        console.log("reportId: ", report.report_id);
        console.log("siteId: ", reportSite.site_id);

        try {
            const response = await fetch(`/api/SaveReportSite`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    report_id: report.report_id,
                    site_id: reportSite.site_id,
                    checked: reportSite.checked,
                    screenshot_urls: reportSite.screenshot_urls
                })
            });

            const data = await response.json() as SaveReportSiteResponse;

            if (data.status === 'success') {
                // Clear unsaved changes flag after successful save
                unsavedChanges[reportSite.site_id] = false;
                toast.success('Site data saved successfully');
            } else {
                toast.error(data.message || 'Failed to save site data [1]');
            }
        } catch (error) {
            console.error('Error saving site data:', error);
            toast.error('Failed to save site data [2]');
        }
    }

    async function generateReport(): Promise<void> {
        try {
            const response = await fetch(`/api/GenerateReport/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    report_id: report.report_id
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `report.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                a.remove();

                toast.success('Report downloaded successfully');
            } else {
                try {
                    const errorData = await response.json() as GenerateReportResponse;
                    toast.error(errorData.message || 'Failed to generate report [1]');
                } catch (e) {
                    toast.error('Failed to generate report [2]');
                }
            }
        } catch (error) {
            console.error('Error generating report:', error);
            toast.error('Failed to generate report [3]');
        }
    }

    function hasUnsavedChanges(siteId: string): boolean {
        return !!unsavedChanges[siteId];
    }

    function markAsChanged(siteId: string): void {
        unsavedChanges[siteId] = true;

        // Clear any existing timer for this site
        if (saveTimers[siteId]) {
            clearTimeout(saveTimers[siteId]);
        }

        // Set a new timer to save after 2 seconds
        saveTimers[siteId] = setTimeout(() => {
            const reportSite = reportSites.find(site => site.site_id === siteId);
            if (reportSite) {
                saveReportSite(reportSite);
            }
        }, 2000) as unknown as number;
    }

    onMount(async () => {
        try {
            const response = await fetch(`/api/GetLabels`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json() as GetLabelsResponse;

            if (data.status === 'success') {
                labels = data.labels;
                labelsByGroup = labels.reduce((acc: Record<string, Label[]>, label: Label) => {
                    if (!acc[label.label_group]) acc[label.label_group] = [];
                    acc[label.label_group].push(label);
                    return acc;
                }, {});

                // Find the 'Approved' label and set it as the default status filter
                const approvedLabel = getLabelByName('Approved', labels);
                if (approvedLabel) {
                    selectOptions.status = approvedLabel.label_id;
                }
            }
        } catch (error) {
            console.error('Error fetching labels:', error);
            toast.error('Failed to load labels');
        }

        await getReportSites();
    });

    onDestroy(() => {
        Object.values(saveTimers).forEach(timerId => {
            clearTimeout(timerId);
        });
    });
</script>

<Head
    title={`Report: ${report.report_website_url} - ${PUBLIC_SITE_NAME}`}
    description="Report: {report.report_website_url}"
    url={`${PUBLIC_SITE_URL}/dashboard/report/${report.report_id}`}
/>


<section class="flex-1 bg-secondary py-8 lg:py-24">
    <div class="container">
        <div class="mb-12 flex items-center justify-between">
            <h1 class="text-3xl font-bold tracking-tight text-white md:text-4xl lg:text-5xl">Report</h1>
            <button class="btn-primary w-fit" onclick={generateReport}>
                Generate XLSX ({checkedCount})
            </button>
        </div>

        <!-- Report Info Fields -->
        <div class="mb-8 overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 p-4">
                <div class="relative">
                    <label for="website" class="absolute -top-[8px] left-2 bg-white px-1 text-xs font-bold text-black">
                        Website URL:
                    </label>
                    <input
                        id="website"
                        type="text"
                        class="flex w-full appearance-none items-center justify-between rounded border border-gray-400 bg-transparent p-3 text-sm text-black hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        value={report?.report_website_url ?? ''}
                        readonly
                    />
                </div>
                <div class="relative">
                    <label for="email" class="absolute -top-[8px] left-2 bg-white px-1 text-xs font-bold text-black">
                        Email Address:
                    </label>
                    <input
                        id="email"
                        type="text"
                        class="flex w-full appearance-none items-center justify-between rounded border border-gray-400 bg-transparent p-3 text-sm text-black hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        value={report?.report_email_address ?? ''}
                        readonly
                    />
                </div>
                <div class="relative">
                    <label for="password" class="absolute -top-[8px] left-2 bg-white px-1 text-xs font-bold text-black">
                        Email Password:
                    </label>
                    <input
                        id="password"
                        type="text"
                        class="flex w-full appearance-none items-center justify-between rounded border border-gray-400 bg-transparent p-3 text-sm text-black hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        value={report?.report_email_password ?? ''}
                        readonly
                    />
                </div>
            </div>
        </div>

        <div class="relative overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
            <div class="flex flex-col items-start justify-between gap-4 border-b border-gray-300 p-4 lg:flex-row lg:items-center">
                <div class="grid w-full gap-4 sm:grid-cols-2 md:grid-cols-4">
                    <CustomDropdown
                        onChange={() => getReportSites()}
                        bind:value={selectOptions.type}
                        labels={labelsByGroup['type']}
                        placeholder="All"
                        title="Type:"
                    />
                    <CustomDropdown
                        onChange={() => getReportSites()}
                        bind:value={selectOptions.pricing}
                        labels={labelsByGroup['pricing']}
                        placeholder="All"
                        title="Pricing:"
                    />
                    <CustomDropdown
                        onChange={() => getReportSites()}
                        bind:value={selectOptions.status}
                        labels={labelsByGroup['status']}
                        placeholder="All"
                        title="Status:"
                    />
                    <CustomDropdown
                        onChange={() => getReportSites()}
                        bind:value={selectOptions.link_type}
                        labels={labelsByGroup['link-type']}
                        placeholder="All"
                        title="Link Type:"
                    />
                    <CustomDropdown
                        onChange={() => getReportSites()}
                        bind:value={selectOptions.login}
                        labels={labelsByGroup['login']}
                        placeholder="All"
                        title="Login Type:"
                    />
                    <CustomDropdown
                        onChange={() => getReportSites()}
                        bind:value={selectOptions.author_info}
                        labels={labelsByGroup['author-info']}
                        placeholder="All"
                        title="Author Info:"
                    />
                    <CustomDropdown
                        onChange={() => getReportSites()}
                        bind:value={selectOptions.confirmation}
                        labels={labelsByGroup['confirmation']}
                        placeholder="All"
                        title="Confirmation:"
                    />
                    <CustomDropdown
                        onChange={() => getReportSites()}
                        bind:value={selectOptions.sort_by}
                        labels={sortOptions}
                        placeholder=""
                        title="Sort by:"
                    />
                </div>
            </div>

            <div class="overflow-x-auto">
                <div class="overflow-auto">
                    <table class="w-full table-fixed text-left text-sm font-semibold text-gray-500">
                        <thead class="bg-white text-xs uppercase text-gray-600">
                            <tr class="border-b border-gray-300">
                                <th scope="col" class="w-[60px] whitespace-nowrap py-4 pl-4 pr-2">#</th>
                                <th scope="col" class="w-[50px] whitespace-nowrap px-2 py-4"></th>
                                <th scope="col" class="w-[50px] whitespace-nowrap px-2 py-4"></th>
                                <th scope="col" class="w-[210px] whitespace-nowrap px-2 py-4">Name</th>
                                <th scope="col" class="w-[50px] whitespace-nowrap px-2 py-4">DR</th>
                                <th scope="col" class="w-[80px] whitespace-nowrap px-2 py-4">Visits</th>
                                <th scope="col" class="w-[140px] whitespace-nowrap px-2 py-4">Type</th>
                                <th scope="col" class="w-[80px] whitespace-nowrap px-2 py-4">Pricing</th>
                                <th scope="col" class="w-[110px] whitespace-nowrap px-2 py-4">Link Type</th>
                                <th scope="col" class="w-[200px] whitespace-nowrap px-2 py-4">Login Type</th>
                                <th scope="col" class="w-[250px] whitespace-nowrap py-4 pl-2 pr-4">Screenshots</th>
                            </tr>
                        </thead>
                        {#if isLoading}
                            <tbody class="text-sm text-black">
                                <tr>
                                    <td colspan="11" class="py-4 text-center">
                                        <div class="flex items-center justify-center gap-2">
                                            <div class="h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-gray-400"></div>
                                            <span class="font-semibold">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        {:else}
                            <tbody class="text-sm text-black">
                                {#each reportSites as reportSite, index}
                                    <tr class="border-b border-gray-300 {hasUnsavedChanges(reportSite.site_id) ? 'bg-[#ffff9980]' : reportSite.checked ? 'bg-[#33ff3336]' : ''} hover:bg-gray-200">
                                        <td class="whitespace-nowrap py-4 pl-4 pr-2">{index + 1}.</td>
                                        <td class="px-2 py-4">
                                            <button
                                                class="rounded-full border border-gray-400 bg-white p-2 hover:bg-gray-100"
                                                aria-label="Save"
                                                onclick={() => saveReportSite(reportSite)}
                                            >
                                                <svg class="h-4 w-4 stroke-current">
                                                    <use href="#icon-save"></use>
                                                </svg>
                                            </button>
                                        </td>
                                        <td class="px-2 py-4">
                                            <input
                                                type="checkbox"
                                                bind:checked={reportSite.checked}
                                                onchange={() => markAsChanged(reportSite.site_id)}
                                                class="h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary"
                                            />
                                        </td>
                                        <td class="px-2 py-4">{reportSite.site_name}</td>
                                        <td class="px-2 py-4">{reportSite.site_dr}</td>
                                        <td class="px-2 py-4">{formatVisits(reportSite.site_visits)}</td>
                                        <td class="px-2 py-4">
                                            {#if reportSite.label_ids && labels.length > 0}
                                                <div class="flex flex-wrap gap-2">
                                                    {#each reportSite.label_ids.filter(id => labels.find(l => l.label_id === id && l.label_group === 'type')) as label_id}
                                                        <div
                                                            style="background:{getLabelbyId(label_id, labels)?.label_color}1A;border-color:{getLabelbyId(label_id, labels)?.label_color};color:{getLabelbyId(label_id, labels)?.label_color}"
                                                            class="w-fit whitespace-nowrap rounded-2xl border border-gray-400 bg-white px-2 py-1 text-xs leading-none"
                                                        >
                                                            {getLabelbyId(label_id, labels)?.label_name}
                                                        </div>
                                                    {/each}
                                                </div>
                                            {/if}
                                        </td>
                                        <td class="px-2 py-4">
                                            {#if reportSite.label_ids && labels.length > 0}
                                                <div class="flex flex-wrap gap-2">
                                                    {#each reportSite.label_ids.filter(id => labels.find(l => l.label_id === id && l.label_group === 'pricing')) as label_id}
                                                        <div
                                                            style="background:{getLabelbyId(label_id, labels)?.label_color}1A;border-color:{getLabelbyId(label_id, labels)?.label_color};color:{getLabelbyId(label_id, labels)?.label_color}"
                                                            class="w-fit whitespace-nowrap rounded-2xl border border-gray-400 bg-white px-2 py-1 text-xs leading-none"
                                                        >
                                                            {getLabelbyId(label_id, labels)?.label_name}
                                                        </div>
                                                    {/each}
                                                </div>
                                            {/if}
                                        </td>
                                        <td class="px-2 py-4">
                                            {#if reportSite.label_ids && labels.length > 0}
                                                <div class="flex flex-wrap gap-2">
                                                    {#each reportSite.label_ids.filter(id => labels.find(l => l.label_id === id && l.label_group === 'link-type')) as label_id}
                                                        <div
                                                            style="background:{getLabelbyId(label_id, labels)?.label_color}1A;border-color:{getLabelbyId(label_id, labels)?.label_color};color:{getLabelbyId(label_id, labels)?.label_color}"
                                                            class="w-fit whitespace-nowrap rounded-2xl border border-gray-400 bg-white px-2 py-1 text-xs leading-none"
                                                        >
                                                            {getLabelbyId(label_id, labels)?.label_name}
                                                        </div>
                                                    {/each}
                                                </div>
                                            {/if}
                                        </td>
                                        <td class="px-2 py-4">
                                            {#if reportSite.label_ids && labels.length > 0}
                                                <div class="flex flex-wrap gap-2">
                                                    {#each reportSite.label_ids.filter(id => labels.find(l => l.label_id === id && l.label_group === 'login')) as label_id}
                                                        <div
                                                            style="background:{getLabelbyId(label_id, labels)?.label_color}1A;border-color:{getLabelbyId(label_id, labels)?.label_color};color:{getLabelbyId(label_id, labels)?.label_color}"
                                                            class="w-fit whitespace-nowrap rounded-2xl border border-gray-400 bg-white px-2 py-1 text-xs leading-none"
                                                        >
                                                            {getLabelbyId(label_id, labels)?.label_name}
                                                        </div>
                                                    {/each}
                                                </div>
                                            {/if}
                                        </td>
                                        <td class="py-2 pl-2 pr-4">
                                            <MultiImageUploader
                                                bind:value={reportSite.screenshot_urls}
                                                onChange={() => markAsChanged(reportSite.site_id)}
                                            />
                                        </td>
                                    </tr>
                                {/each}
                            </tbody>
                        {/if}
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>
