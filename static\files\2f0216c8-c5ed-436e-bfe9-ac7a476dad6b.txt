CREATE TABLE projects (
    project_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_name TEXT NOT NULL DEFAULT 'Unnamed',
    project_document_count INT NOT NULL DEFAULT 0,
    project_word_count INT NOT NULL DEFAULT 0,
    project_updated_at BIGINT NOT NULL DEFAULT 0,
    project_created_at BIGINT NOT NULL DEFAULT 0
);

CREATE TABLE documents (
    document_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID NOT NULL,
    document_title TEXT NOT NULL DEFAULT 'Untitled',
    document_content TEXT NOT NULL DEFAULT '',
    document_word_count INT NOT NULL DEFAULT 0,
    document_updated_at BIGINT NOT NULL DEFAULT 0,
    document_created_at BIGINT NOT NULL DEFAULT 0,
    CONSTRAINT fk_documents_project_id FOREIGN KEY (project_id) REFERENCES projects (project_id) ON DELETE CASCADE
);
