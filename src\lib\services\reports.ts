import sql from '$lib/db/db';
import type { Report } from '$lib/types/tables';
import { generateUUID, isValidUUID } from '$lib/utils/uuid';

export async function getReports(): Promise<Report[]> {
    const reports = await sql<Report[]>`
        SELECT
            report_id, report_website_url, report_email_address, report_email_password,
            report_created_at, report_completed
        FROM reports
        ORDER BY report_created_at DESC
    `;

    return reports;
}

export async function getReport(reportId: string): Promise<Report> {
    const reports = await sql<Report[]>`
        SELECT
            report_id, report_website_url, report_email_address, report_email_password,
            report_created_at, report_completed
        FROM reports
        WHERE report_id = ${reportId}
    `;

    if (reports.length === 0) {
        throw new Error(`No report found with id ${reportId}`);
    }

    return reports[0];
}

export async function reportExists(reportId: string): Promise<Report | null> {
    // Check if it's a valid UUID
    if (isValidUUID(reportId)) {
        try {
            // Try to find by ID
            const resultById = await sql<Report[]>`
                SELECT
                    report_id, report_website_url, report_email_address, report_email_password,
                    report_created_at, report_completed
                FROM reports
                WHERE report_id = ${reportId}
            `;

            if (resultById.length > 0) {
                return resultById[0];
            }
        } catch (error) {
            console.error('Error finding report by UUID:', error);
        }
    }

    return null;
}

export async function saveReport(report_id: string | undefined, report_website_url: string, report_email_address: string, report_email_password: string, report_completed?: boolean): Promise<Report> {
    const currentTime = Math.floor(Date.now() / 1000);
    const id = report_id || generateUUID();

    const result = await sql<Report[]>`
        INSERT INTO reports (report_id, report_website_url, report_email_address, report_email_password, report_created_at, report_completed)
        VALUES (${id}, ${report_website_url}, ${report_email_address}, ${report_email_password}, ${currentTime}, ${report_completed || false})
        ON CONFLICT (report_id)
        DO UPDATE SET
            report_website_url = EXCLUDED.report_website_url,
            report_email_address = EXCLUDED.report_email_address,
            report_email_password = EXCLUDED.report_email_password,
            report_completed = EXCLUDED.report_completed
        RETURNING *
    `;

    return result[0];
}

export async function deleteReport(reportId: string): Promise<void> {
    const result = await sql`
        DELETE FROM reports
        WHERE report_id = ${reportId}
        RETURNING report_id
    `;

    if (result.length === 0) {
        throw new Error('No report found with the given ID');
    }
}

