export interface SiteRequest {
    site_name: string;
    site_url: string;
    site_submit_url: string;
    site_actual_submit_url: string;
    site_note: string;
    site_waiting_time: string;
}

export interface CreateSiteRequest extends SiteRequest {
    label_ids: string[];
}

export interface UpdateSiteRequest extends SiteRequest {
    site_id: string;
    label_ids: string[];
}

export interface DeleteSiteRequest {
    site_id: string;
}

export interface GetSitesRequest {
    andLabelIds?: string[];
    orLabelIds?: string[];
    page_num?: number;
    per_page?: number;
    sort?: string;
    search?: string;
}

export interface GetLabelsRequest {
    // No parameters needed for this request
}

export interface SaveLabelRequest {
    label_id?: string;
    label_name: string;
    label_group: string;
    label_color?: string;
    label_order?: number;
}

export interface DeleteLabelRequest {
    label_id: string;
}

export interface GetReportRequest {
  report_id: string;
  label_ids?: string[];
  page_num?: number;
  per_page?: number;
  sort?: string;
}

export interface GetReportsRequest {
    page_num?: number;
    per_page?: number;
}

export interface GetReportSiteRequest {
    report_id: string;
    site_id: string;
}

export interface GetReportSitesRequest {
    report_id: string;
    label_ids?: string[];
    page_num?: number;
    per_page?: number;
    sort?: string;
}

export interface SaveReportRequest {
    report_id?: string;
    report_website_url: string;
    report_email_address: string;
    report_email_password: string;
    report_completed?: boolean;
}

export interface DeleteReportRequest {
    report_id: string;
}

export interface SaveReportSiteRequest {
    report_id: string;
    site_id: string;
    checked?: boolean;
    screenshot_urls?: string[];
}

export interface GenerateReportRequest {
    report_id: string;
}

export interface LoginRequest {
    username: string;
    password: string;
}

export interface SubmitFormRequest {
    [key: string]: string;
}

export interface UploadImageRequest {
    image: File;
}

export interface GetFormFieldsRequest {
  report_id: string;
}

export interface UpdateSiteFormFieldsRequest {
    site_id: string;
    form_fields: string;
}

export interface FixFormFieldsJSONRequest {
    site_id: string;
}

export interface GetSiteJSONRequest {
    url: string;
}

export interface SaveSiteJSONRequest {
    site_id: string;
    json_data: string;
}

export interface SubmitURLExistsRequest {
    url: string;
}

//

export interface GetURLRequest {
    sortby: 'dr' | 'visits';
}

export interface SaveDRRequest {
    dr: string;
    url: string;
}

export interface SaveVisitsRequest {
    visits: string;
    url: string;
}

export interface SaveNoteRequest {
    note_id?: string;
    note_title: string;
    note_content: string;
    note_files?: string;
}

export interface DeleteNoteRequest {
    note_id: string;
}