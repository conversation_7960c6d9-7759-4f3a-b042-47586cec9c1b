import { reportExists } from '$lib/services/reports';
import type { PageServerLoad } from './$types';
import type { Report } from '$lib/types/tables';

export const load: PageServerLoad = async ({ params }) => {
  const report = await reportExists(params.report_id) as Report | null;
  
  if (!report) {
    return {
      status: 'error',
      message: 'Report not found'
    }
  }
  
  return {
    status: 'success',
    message: 'Report found',
    report
  }
}
