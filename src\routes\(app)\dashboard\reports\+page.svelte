<script lang="ts">
    import { toast } from 'svelte-sonner';
    import { onMount } from 'svelte';
    import type { Report } from '$lib/types/tables';
    import type { GetReportsResponse, SaveReportResponse, DeleteReportResponse } from '$lib/types/responses';
    import Head from "$lib/components/layout/Head.svelte";
    import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';

    let reports = $state<Report[]>([]);
    let isLoading = $state(true);

    async function getReports(): Promise<void> {
        try {
            isLoading = true;
            const response = await fetch(`/api/GetReports`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json() as GetReportsResponse;

            if (data.status === 'success') {
                reports = data.reports;

                console.log("Reports: ", reports);

                if (!reports || reports.length === 0) {
                    toast.message('No reports found');
                }
            } else {
                toast.error(data.message || 'Failed to load reports');
            }
        } catch (error) {
            toast.error('Failed to load reports');
            console.error(error);
        } finally {
            isLoading = false;
        }
    }

    async function saveReport(report: Report): Promise<void> {
        try {
            const response = await fetch('/api/SaveReport', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(report),
            });
            const data = await response.json() as SaveReportResponse;

            if (data.status === 'success') {
                if (!report.report_id) {
                    report.report_id = data.report.report_id;
                    report.report_created_at = data.report.report_created_at;
                }
                toast.success(data.message);
            } else {
                toast.error(data.message || 'An error occurred');
            }
        } catch (error) {
            toast.error('Failed to update report');
            console.error(error);
        }
    }

    async function deleteReport(report: Report): Promise<void> {
        if (!confirm(`Are you sure you want to delete the report for ${report.report_website_url || 'this website'}?`)) {
            return;
        }

        if (!report.report_id) {
            reports = reports.filter(r => r !== report);
            return;
        }

        try {
            const response = await fetch(`/api/DeleteReport`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ report_id: report.report_id })
            });
            const data = await response.json() as DeleteReportResponse;

            if (data.status === 'success') {
                toast.success(data.message);
            } else {
                toast.error(data.message || 'An error occurred');
                return;
            }
        } catch (error) {
            toast.error('Failed to delete report');
            console.error(error);
            return;
        }

        reports = reports.filter(r => r !== report);
    }

    onMount(async () => {
        await getReports();
    });
</script>

<Head
    title={`Reports - ${PUBLIC_SITE_NAME}`}
    description="Manage your SaaS directory submission reports"
    url={`${PUBLIC_SITE_URL}/dashboard/reports`}
/>

<section class="flex-1 bg-secondary py-8 lg:py-24">
    <div class="container-xl">
        <div class="mb-12 flex items-center justify-between">
            <h1 class="text-3xl font-bold tracking-tight text-white md:text-4xl lg:text-5xl">Reports</h1>
            <button
                onclick={() => {
                    reports = [
                        {
                            report_id: '',
                            report_website_url: '',
                            report_email_address: '',
                            report_email_password: '',
                            report_created_at: 0,
                        },
                        ...reports,
                    ];
                }}
                class="btn-primary w-fit"
            >
                <svg class="h-4 w-4 fill-white">
                    <use href="#icon-plus"></use>
                </svg>
                New
            </button>
        </div>

        <div class="relative overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
            <div class="overflow-x-auto">
                <div class="overflow-auto">
                    <table class="w-full table-fixed text-left text-sm font-semibold text-gray-500">
                        <thead class="bg-white text-xs uppercase text-gray-600">
                            <tr class="border-b border-gray-300">
                                <th scope="col" class="w-[60px] whitespace-nowrap py-4 pl-4 pr-2">#</th>
                                <th scope="col" class="w-[90px] whitespace-nowrap px-2 py-4">Actions</th>
                                <th scope="col" class="w-[245px] whitespace-nowrap px-2 py-4">Client</th>
                                <th scope="col" class="w-[245px] whitespace-nowrap px-2 py-4">Email Address</th>
                                <th scope="col" class="w-[245px] whitespace-nowrap px-2 py-4">Email Password</th>
                                <th scope="col" class="w-[120px] whitespace-nowrap px-2 py-4 text-center">Completed?</th>
                                <th scope="col" class="w-[125px] whitespace-nowrap px-2 py-4">Form URL</th>
                                <th scope="col" class="w-[125px] whitespace-nowrap px-2 py-4">Report URL (A)</th>
                                <th scope="col" class="w-[125px] whitespace-nowrap px-2 py-4">Report URL (C)</th>
                                <th scope="col" class="w-[180px] whitespace-nowrap py-4 pl-2 pr-4">Created At</th>
                            </tr>
                        </thead>
                        {#if isLoading}
                            <tbody class="text-sm text-black">
                                <tr>
                                    <td colspan="10" class="py-4 text-center">
                                        <div class="flex items-center justify-center gap-2">
                                            <div
                                                class="h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-gray-400"
                                            ></div>
                                            <span class="font-semibold">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        {:else if !reports || reports.length === 0}
                            <tbody class="text-sm text-black">
                                <tr>
                                    <td colspan="10" class="py-4 text-center">
                                        <div class="flex items-center justify-center gap-2">
                                            <span class="font-semibold">No reports found</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        {:else}
                            <tbody class="text-sm text-black">
                                {#each reports as report, index}
                                    <tr class="border-b border-gray-300 hover:bg-gray-200">
                                        <td class="py-2 pl-4 pr-2">
                                            {index + 1}.
                                        </td>
                                        <td class="w-[90px] px-2 py-2">
                                            <div class="flex items-center gap-1">
                                                <button
                                                    onclick={() => saveReport(report)}
                                                    class="rounded-full border border-gray-400 bg-white p-2 hover:bg-gray-100"
                                                    aria-label="Save"
                                                >
                                                    <svg class="h-4 w-4 stroke-current">
                                                        <use href="#icon-save"></use>
                                                    </svg>
                                                </button>
                                                <button
                                                    onclick={() => deleteReport(report)}
                                                    class="rounded-full border border-gray-400 bg-white p-2 hover:bg-gray-100"
                                                    aria-label="Delete"
                                                >
                                                    <svg class="h-4 w-4 stroke-current">
                                                        <use href="#icon-delete"></use>
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                        <td class="px-2 py-2">
                                            <input
                                                class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                                                type="text"
                                                bind:value={report.report_website_url}
                                            />
                                        </td>
                                        <td class="px-2 py-2">
                                            <input
                                                class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                                                type="text"
                                                bind:value={report.report_email_address}
                                            />
                                        </td>
                                        <td class="px-2 py-2">
                                          <input
                                            class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                                            type="text"
                                            bind:value={report.report_email_password}
                                          />
                                        </td>
                                        <td class="px-2 py-2">
                                          <input class="block mx-auto h-5 w-5" type="checkbox" bind:checked={report.report_completed} />
                                        </td>
                                        <td class="px-2 py-2">
                                          <div class="flex items-center gap-2">
                                              <a
                                                  href="/dashboard/form/{report.report_id}"
                                                  target="_blank"
                                                  class="flex w-fit items-center gap-2 text-black"
                                                  aria-label="Visit Form URL"
                                              >
                                                  <svg class="h-3 w-3 fill-current">
                                                      <use href="#icon-external"></use>
                                                  </svg>
                                                  Visit URL
                                              </a>
                                          </div>
                                        </td>
                                        <td class="px-2 py-2">
                                            <div class="flex items-center gap-2">
                                                <a
                                                    href="/dashboard/report/{report.report_id}"
                                                    target="_blank"
                                                    class="flex w-fit items-center gap-2 text-black"
                                                    aria-label="Visit Report URL"
                                                >
                                                    <svg class="h-3 w-3 fill-current">
                                                        <use href="#icon-external"></use>
                                                    </svg>
                                                    Visit URL
                                                </a>
                                            </div>
                                        </td>
                                        <td class="px-2 py-2">
                                            <div class="flex items-center gap-2">
                                                <a
                                                    href="/report/{report.report_id}"
                                                    target="_blank"
                                                    class="flex w-fit items-center gap-2 text-black"
                                                    aria-label="Visit Report URL"
                                                >
                                                    <svg class="h-3 w-3 fill-current">
                                                        <use href="#icon-external"></use>
                                                    </svg>
                                                    Visit URL
                                                </a>
                                            </div>
                                        </td>
                                        <td class="py-2 pl-2 pr-4">
                                            {report.report_created_at
                                                ? new Date(Number(report.report_created_at) * 1000).toLocaleDateString('en-US', {
                                                    month: 'long',
                                                    day: 'numeric',
                                                    year: 'numeric',
                                                })
                                                : 'N/A'}
                                        </td>
                                    </tr>
                                {/each}
                            </tbody>
                        {/if}
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>
